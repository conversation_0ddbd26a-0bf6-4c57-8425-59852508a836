package com.tqhit.battery.one.features.emoji.di

import com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl
import com.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt module for providing dependencies for the emoji battery feature.
 *
 * This module follows the stats module architecture pattern and provides
 * concrete bindings for the emoji battery feature components.
 *
 * Architecture Integration:
 * - Follows established stats module DI patterns
 * - Integrates with CoreBatteryStatsService for battery data
 * - Supports clean architecture separation (Data, Domain, Presentation layers)
 * - Uses Singleton scope for shared dependencies
 * - Uses Firebase Remote Config with local JSON fallback
 *
 * Phase 1 Bindings:
 * - BatteryStyleRepository interface binding ✅
 *
 * Future Bindings (to be added in later phases):
 * - CustomizationRepository interface binding
 * - Use case bindings for domain layer
 * - Cache implementations for data persistence
 *
 * @see com.tqhit.battery.one.features.stats.charge.di.StatsChargeDIModule
 * @see com.tqhit.battery.one.features.stats.health.di.HealthDIModule
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class EmojiBatteryDIModule {

    /**
     * Binds the BatteryStyleRepository interface to BatteryStyleRepositoryImpl.
     * This ensures that whenever BatteryStyleRepository is injected,
     * the BatteryStyleRepositoryImpl implementation will be provided.
     *
     * The implementation uses Firebase Remote Config with local JSON fallback
     * following the established pattern from the animation feature.
     *
     * @param batteryStyleRepositoryImpl The implementation with Firebase Remote Config integration
     * @return The BatteryStyleRepository interface
     */
    @Binds
    @Singleton
    abstract fun bindBatteryStyleRepository(
        batteryStyleRepositoryImpl: BatteryStyleRepositoryImpl
    ): BatteryStyleRepository

    // Future bindings will be added in subsequent phases:
    //
    // @Binds
    // @Singleton
    // abstract fun bindCustomizationRepository(
    //     implementation: CustomizationRepositoryImpl
    // ): CustomizationRepository
}
