<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Selected state animation -->
    <item android:state_selected="true">
        <set android:ordering="together">
            <objectAnimator
                android:propertyName="scaleX"
                android:duration="200"
                android:valueTo="1.05"
                android:valueType="floatType"
                android:interpolator="@android:interpolator/fast_out_slow_in" />
            <objectAnimator
                android:propertyName="scaleY"
                android:duration="200"
                android:valueTo="1.05"
                android:valueType="floatType"
                android:interpolator="@android:interpolator/fast_out_slow_in" />
            <objectAnimator
                android:propertyName="alpha"
                android:duration="200"
                android:valueTo="1.0"
                android:valueType="floatType"
                android:interpolator="@android:interpolator/fast_out_slow_in" />
        </set>
    </item>
    
    <!-- Pressed state animation -->
    <item android:state_pressed="true">
        <set android:ordering="together">
            <objectAnimator
                android:propertyName="scaleX"
                android:duration="100"
                android:valueTo="0.95"
                android:valueType="floatType"
                android:interpolator="@android:interpolator/fast_out_slow_in" />
            <objectAnimator
                android:propertyName="scaleY"
                android:duration="100"
                android:valueTo="0.95"
                android:valueType="floatType"
                android:interpolator="@android:interpolator/fast_out_slow_in" />
        </set>
    </item>
    
    <!-- Default state animation -->
    <item>
        <set android:ordering="together">
            <objectAnimator
                android:propertyName="scaleX"
                android:duration="200"
                android:valueTo="1.0"
                android:valueType="floatType"
                android:interpolator="@android:interpolator/fast_out_slow_in" />
            <objectAnimator
                android:propertyName="scaleY"
                android:duration="200"
                android:valueTo="1.0"
                android:valueType="floatType"
                android:interpolator="@android:interpolator/fast_out_slow_in" />
            <objectAnimator
                android:propertyName="alpha"
                android:duration="200"
                android:valueTo="0.7"
                android:valueType="floatType"
                android:interpolator="@android:interpolator/fast_out_slow_in" />
        </set>
    </item>
    
</selector>
