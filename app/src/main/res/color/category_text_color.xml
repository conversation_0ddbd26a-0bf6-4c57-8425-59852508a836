<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- Selected state text color - same as unselected for consistency with Animation fragment -->
    <item android:state_selected="true" android:color="?attr/black" />

    <!-- Pressed state text color -->
    <item android:state_pressed="true" android:color="?attr/black" />

    <!-- Default state text color -->
    <item android:color="?attr/black" />

</selector>
