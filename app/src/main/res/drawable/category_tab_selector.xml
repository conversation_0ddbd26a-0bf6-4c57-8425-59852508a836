<?xml version="1.0" encoding="utf-8"?>
<!--
    Emoji category tabs now use the same drawables as Animation fragment for perfect consistency.
    This file is kept for backward compatibility but the actual drawable switching is done
    programmatically in CategoryAdapter using grey_block_line_up.xml and grey_block_selected_line_up.xml
-->
<selector xmlns:android="http://schemas.android.com/apk/res/android" android:exitFadeDuration="250">

    <!-- Reference to Animation fragment's unselected drawable -->
    <item>
        <shape android:tint="?attr/grey">
            <size
                android:height="10dp"
                android:width="10dp"/>
            <corners
                android:topLeftRadius="10dp"
                android:topRightRadius="10dp"
                android:bottomLeftRadius="4dp"
                android:bottomRightRadius="4dp"/>
        </shape>
    </item>

</selector>
