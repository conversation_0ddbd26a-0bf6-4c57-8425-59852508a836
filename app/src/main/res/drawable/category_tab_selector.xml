<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android" android:exitFadeDuration="200">
    
    <!-- Selected state -->
    <item android:state_selected="true">
        <layer-list>
            <!-- Background -->
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="?attr/colorPrimary" />
                    <corners
                        android:topLeftRadius="15dp"
                        android:topRightRadius="15dp"
                        android:bottomLeftRadius="4dp"
                        android:bottomRightRadius="4dp" />
                    <stroke
                        android:width="2dp"
                        android:color="?attr/colorr" />
                </shape>
            </item>
            <!-- Selection indicator (bottom line) -->
            <item android:gravity="bottom">
                <shape android:shape="rectangle">
                    <solid android:color="?attr/colorr" />
                    <size android:height="3dp" />
                </shape>
            </item>
        </layer-list>
    </item>
    
    <!-- Pressed state -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="?attr/grey_pressed" />
            <corners
                android:topLeftRadius="12dp"
                android:topRightRadius="12dp"
                android:bottomLeftRadius="4dp"
                android:bottomRightRadius="4dp" />
            <stroke
                android:width="1dp"
                android:color="?attr/colorPrimary" />
        </shape>
    </item>
    
    <!-- Default state -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="?attr/grey" />
            <corners
                android:topLeftRadius="10dp"
                android:topRightRadius="10dp"
                android:bottomLeftRadius="4dp"
                android:bottomRightRadius="4dp" />
            <stroke
                android:width="1dp"
                android:color="?attr/grey_pressed" />
        </shape>
    </item>
    
</selector>
